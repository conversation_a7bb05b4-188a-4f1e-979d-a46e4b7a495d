#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时训练监控脚本
监控PPASR方言训练过程，实时显示训练状态和CER变化
"""

import os
import time
import json
import re
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from collections import deque
import threading

class TrainingMonitor:
    def __init__(self, log_file="logs/training.log", max_points=100):
        self.log_file = log_file
        self.max_points = max_points
        self.training_data = {
            'epochs': deque(maxlen=max_points),
            'steps': deque(maxlen=max_points),
            'loss': deque(maxlen=max_points),
            'cer': deque(maxlen=max_points),
            'timestamps': deque(maxlen=max_points)
        }
        self.last_position = 0
        self.is_running = True
        
    def parse_log_line(self, line):
        """解析日志行，提取训练信息"""
        try:
            # 匹配训练loss信息
            loss_pattern = r'epoch: (\d+), step: (\d+), loss: ([\d.]+)'
            loss_match = re.search(loss_pattern, line)
            
            # 匹配CER信息
            cer_pattern = r'CER: ([\d.]+)%'
            cer_match = re.search(cer_pattern, line)
            
            # 匹配时间戳
            timestamp_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
            timestamp_match = re.search(timestamp_pattern, line)
            
            result = {}
            if loss_match:
                result['epoch'] = int(loss_match.group(1))
                result['step'] = int(loss_match.group(2))
                result['loss'] = float(loss_match.group(3))
                
            if cer_match:
                result['cer'] = float(cer_match.group(1))
                
            if timestamp_match:
                result['timestamp'] = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                
            return result if result else None
            
        except Exception as e:
            return None
    
    def update_data(self, data):
        """更新训练数据"""
        current_time = data.get('timestamp', datetime.now())
        
        if 'epoch' in data and 'step' in data and 'loss' in data:
            self.training_data['epochs'].append(data['epoch'])
            self.training_data['steps'].append(data['step'])
            self.training_data['loss'].append(data['loss'])
            self.training_data['timestamps'].append(current_time)
            
        if 'cer' in data:
            # 如果有CER数据，更新最后一个点的CER
            if len(self.training_data['cer']) < len(self.training_data['loss']):
                self.training_data['cer'].append(data['cer'])
            else:
                self.training_data['cer'][-1] = data['cer']
    
    def monitor_log(self):
        """监控日志文件"""
        print(f"🔍 开始监控训练日志: {self.log_file}")
        
        while self.is_running:
            try:
                if os.path.exists(self.log_file):
                    with open(self.log_file, 'r', encoding='utf-8') as f:
                        f.seek(self.last_position)
                        new_lines = f.readlines()
                        self.last_position = f.tell()
                        
                        for line in new_lines:
                            if not self.is_running:
                                break
                                
                            data = self.parse_log_line(line.strip())
                            if data:
                                self.update_data(data)
                                self.print_status(data)
                
                time.sleep(2)  # 每2秒检查一次
                
            except Exception as e:
                print(f"❌ 监控日志时出错: {e}")
                time.sleep(5)
    
    def print_status(self, data):
        """打印训练状态"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        if 'epoch' in data and 'step' in data and 'loss' in data:
            print(f"[{timestamp}] 📊 Epoch: {data['epoch']}, Step: {data['step']}, Loss: {data['loss']:.4f}")
            
        if 'cer' in data:
            print(f"[{timestamp}] 🎯 CER: {data['cer']:.2f}%")
    
    def get_current_stats(self):
        """获取当前训练统计信息"""
        if not self.training_data['loss']:
            return None
            
        stats = {
            'total_steps': len(self.training_data['steps']),
            'current_epoch': self.training_data['epochs'][-1] if self.training_data['epochs'] else 0,
            'current_step': self.training_data['steps'][-1] if self.training_data['steps'] else 0,
            'current_loss': self.training_data['loss'][-1] if self.training_data['loss'] else 0,
            'best_loss': min(self.training_data['loss']) if self.training_data['loss'] else 0,
            'current_cer': self.training_data['cer'][-1] if self.training_data['cer'] else None,
            'best_cer': min(self.training_data['cer']) if self.training_data['cer'] else None
        }
        
        return stats
    
    def save_progress(self, filename="training_progress.json"):
        """保存训练进度"""
        try:
            progress_data = {
                'epochs': list(self.training_data['epochs']),
                'steps': list(self.training_data['steps']),
                'loss': list(self.training_data['loss']),
                'cer': list(self.training_data['cer']),
                'timestamps': [t.isoformat() for t in self.training_data['timestamps']]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)
                
            print(f"💾 训练进度已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存进度时出错: {e}")
    
    def stop(self):
        """停止监控"""
        self.is_running = False
        print("🛑 停止训练监控")

def main():
    """主函数"""
    print("🚀 PPASR方言训练实时监控器启动")
    print("=" * 50)
    
    monitor = TrainingMonitor()
    
    # 启动监控线程
    monitor_thread = threading.Thread(target=monitor.monitor_log)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    try:
        while True:
            time.sleep(10)  # 每10秒显示一次统计信息
            
            stats = monitor.get_current_stats()
            if stats:
                print("\n" + "="*50)
                print(f"📈 训练统计 [{datetime.now().strftime('%H:%M:%S')}]")
                print(f"当前轮次: {stats['current_epoch']}")
                print(f"当前步数: {stats['current_step']}")
                print(f"当前Loss: {stats['current_loss']:.4f}")
                print(f"最佳Loss: {stats['best_loss']:.4f}")
                if stats['current_cer'] is not None:
                    print(f"当前CER: {stats['current_cer']:.2f}%")
                if stats['best_cer'] is not None:
                    print(f"最佳CER: {stats['best_cer']:.2f}%")
                print("="*50)
                
                # 每分钟保存一次进度
                if stats['current_step'] % 30 == 0:
                    monitor.save_progress()
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断监控")
        monitor.stop()
        monitor.save_progress()

if __name__ == "__main__":
    main()
